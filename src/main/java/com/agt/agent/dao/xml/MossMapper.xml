<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.agt.agent.dao.IMossDao">

	<select id="querySampleData" resultType="java.util.LinkedHashMap">
		SELECT * FROM (${sql}) PAGE OFFSET 0 ROWS FETCH NEXT 3 ROWS ONLY
	</select>

	<sql id="queryDataBySqlSql">
		${sql}
	</sql>

	<select id="queryDataBySqlCount" parameterType="java.util.Map" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="queryDataBySqlSql"/>
        <include refid="global.count_footer"/>
	</select>

	<select id="queryDataBySql" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
        <include refid="queryDataBySqlSql"/>
        <include refid="global.select_footer"/>
	</select>

	<select id="queryChartBySql" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT "${xAxis}" AS "xAxis",
		<foreach collection="yAxis" item="y" separator="," index="i">
			SUM("${y}") AS "yAxis${i}"
		</foreach>
		FROM (
			${sql}
		) GROUP BY "${xAxis}"
		ORDER BY "xAxis"
		OFFSET 0 ROWS FETCH NEXT 256 ROWS ONLY
	</select>

    <insert id="saveGoodEvaluateResult">
        DECLARE
            CLOB_CONTENT CLOB := #{sql, jdbcType=CLOB};
        BEGIN
            MERGE INTO SCPM.SQL_TEMPLATE tgt
            USING (
                SELECT
                    #{question, jdbcType=VARCHAR} AS QUESTION,
                    CLOB_CONTENT AS ANSWER,
                    #{session.userid, jdbcType=VARCHAR} AS CREATE_BY$,
                    sysdate AS CREATE_DATE$,
                    #{templateGroup, jdbcType=VARCHAR} AS TEMPLATE_GROUP,
                    #{rate, jdbcType=NUMERIC} AS RATE,
                    'EVALUATE' AS SOURCE
                FROM dual
            ) src
            ON (tgt.QUESTION = src.QUESTION)
            WHEN MATCHED THEN
                UPDATE SET
                    tgt.ANSWER = src.ANSWER,
                    tgt.CREATE_BY$ = src.CREATE_BY$,
                    tgt.CREATE_DATE$ = src.CREATE_DATE$,
                    tgt.TEMPLATE_GROUP = src.TEMPLATE_GROUP,
                    tgt.RATE = src.RATE,
                    tgt.SOURCE = src.SOURCE
                WHERE tgt.RATE &lt;= src.RATE
            WHEN NOT MATCHED THEN
                INSERT (QUESTION, ANSWER, CREATE_BY$, CREATE_DATE$, TEMPLATE_GROUP, RATE, SOURCE)
                VALUES (
                    src.QUESTION,
                    src.ANSWER,
                    src.CREATE_BY$,
                    src.CREATE_DATE$,
                    src.TEMPLATE_GROUP,
                    src.RATE,
                    src.SOURCE
                );
        END;
    </insert>

    <insert id="saveEvaluateResult">
        DECLARE
            CLOB_CONTENT CLOB := #{sql, jdbcType=CLOB};
        BEGIN
            INSERT INTO SCPM.CHAT_EVALUATE(CHAT_ID, QUESTION, ANSWER, RATE, RATE_REASONS, COMMENTS, CREATE_BY$, CREATE_DATE$)
            VALUES
            (#{chatId, jdbcType=VARCHAR}, #{question, jdbcType=VARCHAR}, CLOB_CONTENT, #{rate, jdbcType=NUMERIC},
            #{rate_reasons, jdbcType=VARCHAR}, #{comments, jdbcType=VARCHAR}, #{session.userid, jdbcType=VARCHAR}, sysdate);
        END;
    </insert>

    <select id="queryTableTips" resultType="java.util.Map">
        SELECT T.OBJ_NAME, T.TIPS FROM SCPM.TAB_COL_TIPS T
         WHERE T.OBJ_TYPE = 'TABLE'
    </select>

    <select id="queryChatLogs" resultType="java.util.Map">
        SELECT T.ID, T.SUBJECT
          FROM SCPM.CHAT_LOGS T
         WHERE T.CREATE_BY$ = #{session.userid, jdbcType=VARCHAR}
         ORDER BY T.CREATE_DATE$ DESC
         FETCH NEXT 6 ROWS ONLY
    </select>

    <select id="queryAllChatLogs" resultType="java.util.Map">
        SELECT T.ID, T.SUBJECT, T.LOGS, T.TOKENS, T.FEE, TO_CHAR(T.CREATE_DATE$, 'YYYY/MM/DD HH24:MI:SS') CREATE_DATE
          FROM SCPM.CHAT_LOGS T
         WHERE T.CREATE_BY$ = #{session.userid, jdbcType=VARCHAR}
         AND T.SUBJECT LIKE '%' || #{keyword, jdbcType=VARCHAR} || '%'
         ORDER BY T.CREATE_DATE$ DESC
         OFFSET #{offset,jdbcType=NUMERIC} ROWS FETCH NEXT 10 ROWS ONLY
    </select>

    <select id="queryChatById" resultType="java.util.Map">
        SELECT T.ID, T.SUBJECT, T.LOGS, T.FEE, T.TOKENS
          FROM SCPM.CHAT_LOGS T
         WHERE T.ID = #{id, jdbcType=VARCHAR}
    </select>

    <select id="querySuggestQuestions" resultType="java.lang.String">
        SELECT DISTINCT T.QUESTION FROM SCPM.SUGGEST_QUESTIONS T WHERE T.ENABLE = 'Y'
    </select>

    <select id="queryAllAgents" resultType="java.util.Map">
        SELECT T.AGENT_ID, T.SUBJECT, T.GROUPS, T.WELCOME_MSG FROM SCPA.MOSS_AGENT_REGISTRATION T
    </select>

    <select id="queryAgentById" resultType="java.util.Map">
        SELECT T.SUBJECT, T.WELCOME_MSG FROM SCPA.MOSS_AGENT_REGISTRATION T WHERE T.AGENT_ID = #{aid, jdbcType=VARCHAR}
    </select>

    <insert id="addToArchive">
        INSERT INTO SCPM.MY_ARCHIVE (ID, QUESTION, CREATE_BY$, CREATE_DATE$) VALUES
        (#{id, jdbcType=VARCHAR}, #{question, jdbcType=VARCHAR}, #{session.userid, jdbcType=VARCHAR}, SYSDATE)
    </insert>

    <select id="queryMyArchive" resultType="java.util.Map">
        SELECT T.ID, T.QUESTION FROM SCPM.MY_ARCHIVE T WHERE T.CREATE_BY$ = #{session.userid, jdbcType=VARCHAR} ORDER BY CREATE_DATE$ DESC
    </select>

    <delete id="deleteMyArchive">
        DELETE FROM SCPM.MY_ARCHIVE T WHERE T.ID = #{id, jdbcType=VARCHAR}
    </delete>
</mapper>
