package com.agt.agent.service.impl;

import com.agt.agent.dao.IMossDao;
import com.agt.agent.feign.MossFeignClient;
import com.agt.agent.service.IMossService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.scp.toolbox.bean.AgentChatParam;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.configuration.database.DatabaseType;
import com.starter.context.configuration.database.DynamicDataSource;
import com.starter.context.configuration.database.TargetDataSource;
import com.starter.login.bean.Session;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Types;
import java.util.*;

@Service
@Scope("prototype")
@Transactional
public class MossServiceImpl implements IMossService {

    @Resource
    private Response response;

    @Resource
    private MossFeignClient mossFeignClient;

    @Resource
    private IMossDao mossDao;

    @Resource
    private ExcelTemplate excelTemplate;

    @Resource
    private DynamicDataSource dynamicDataSource;


    public final static String PARENT_CODE = "menuE11";

    public final static String TERMINATE_TOKEN = "#$终止对话$#";

    @Override
    public Response search(Map<String, Object> parameterMap, Session session) {
        String topic = (String) parameterMap.get("topic");
        JSONArray images = (JSONArray) parameterMap.get("images");

        AgentChatParam agentChatParam = new AgentChatParam();
        agentChatParam.setAgent((String) parameterMap.get("agentId"));
        agentChatParam.setResponse_topic(topic);
        agentChatParam.setUser_id(session.getUserid());
        agentChatParam.getImages().addAll(images.toJavaList(String.class));
        agentChatParam.setQuery((String) parameterMap.get("query"));
        return response.setBody(JSONObject.parseObject(mossFeignClient.agentQuery(agentChatParam)));
    }

    @Override
    public Response createNewConversation(Map<String, Object> parameterMap, Session session) {
        String userid = session.getUserid();
        if (StringUtils.isBlank(userid)) {
            userid = "anonymous";
        }
        String topic = (String) parameterMap.get("topic");
        AgentChatParam agentChatParam = new AgentChatParam();
        agentChatParam.setAgent("no-agent");
        agentChatParam.setResponse_topic(topic);
        agentChatParam.setUser_id(userid);
        agentChatParam.setQuery(TERMINATE_TOKEN);
        return response.setBody(JSONObject.parseObject(mossFeignClient.agentQuery(agentChatParam)));
    }


    @Override
    public Response searchAgentId(Map<String, Object> parameterMap, Session session) {
        String userid = session.getUserid();
        JSONArray images = (JSONArray) parameterMap.get("images");
        String topic = (String) parameterMap.get("topic");
        AgentChatParam agentChatParam = new AgentChatParam();
        agentChatParam.setResponse_topic(topic);
        agentChatParam.setUser_id(userid);
        agentChatParam.getImages().addAll(images.toJavaList(String.class));
        agentChatParam.setQuery((String) parameterMap.get("query"));
        return response.setBody(JSONObject.parseObject(mossFeignClient.agentSelect(agentChatParam)));
    }

    @Override
    @TargetDataSource(DatabaseType.SCP02_READONLY)
    public Response queryDataBySql(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        int total = mossDao.queryDataBySqlCount(parameterMap);
        page.setTotal(total);
        if (total > 0) {
            page.setData(mossDao.queryDataBySql(parameterMap));
        }
        return response.setBody(page);
    }

    public void downloadDataBySql(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        String fileName = "result_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.agt.agent.dao.IMossDao.queryDataBySql", parameterMap);
    }

    @Override
    @TargetDataSource(DatabaseType.SCP02_READONLY)
    public Response initChart(Map<String, Object> parameterMap) {
        return response.setBody(this.queryHeaderBySql((String) parameterMap.get("sql")));
    }

    private Map<String, List<String>> queryHeaderBySql(String rawSql) {
        String sql = "select * from (" + rawSql + ") tt where 1 = 0";

        Map<String, List<String>> resultMap = new HashMap<>();
        List<String> xAxisOpts = new ArrayList<>();
        List<String> yAxisOpts = new ArrayList<>();
        resultMap.put("xAxisOpts", xAxisOpts);
        resultMap.put("yAxisOpts", yAxisOpts);

        try (Connection con = dynamicDataSource.getConnection()) {
            ResultSet rs = con.prepareStatement(sql).executeQuery();
            ResultSetMetaData rsmd = rs.getMetaData();

            for (int i = 1; i <= rsmd.getColumnCount(); i++) {
                String columnName = rsmd.getColumnName(i); // 获取字段名
                int columnType = rsmd.getColumnType(i); // 获取字段类型

                // 判断字段类型
                if (columnType == Types.NUMERIC || columnType == Types.INTEGER || columnType == Types.FLOAT || columnType == Types.DOUBLE) {
                    yAxisOpts.add(columnName); // 属于number类型的字段放入yAxisOpts
                } else if (columnType == Types.VARCHAR || columnType == Types.CHAR || columnType == Types.DATE || columnType == Types.TIMESTAMP) {
                    xAxisOpts.add(columnName); // 属于VARCHAR2, CHAR的字段放入xAxisOpts
                }
            }

            rs.close();
        } catch (Exception e) {
            System.err.println(e.getMessage());
        }
        Collections.sort(xAxisOpts);
        Collections.sort(yAxisOpts);
        return resultMap;
    }

    @Override
    @SuppressWarnings("unchecked")
    @TargetDataSource(DatabaseType.SCP02_READONLY)
    public Response queryChartBySql(Map<String, Object> parameterMap) {
        List<String> yAxis = (List<String>) parameterMap.get("yAxis");

        Map<String, List<Object>> resultMap = new HashMap<>();
        List<Map<String, Object>> dataList = mossDao.queryChartBySql(parameterMap);
        List<Object> xAxisList = resultMap.computeIfAbsent("xAxis", k -> new ArrayList<>());
        for (Map<String, Object> map : dataList) {
            xAxisList.add(map.get("xAxis"));
        }
        for (int i = 0; i < yAxis.size(); i++) {
            String key = "yAxis" + i;
            List<Object> list = resultMap.computeIfAbsent(key, k -> new ArrayList<>());
            for (Map<String, Object> map : dataList) {
                list.add(map.get(key));
            }
        }

        return response.setBody(resultMap);
    }

    @Override
    public Response confirmTable(Map<String, Object> parameterMap, Session session) {
        String topic = (String) parameterMap.get("topic");
        JSONArray arr = ((JSONArray) parameterMap.get("items")).getJSONObject(0).getJSONArray("prefer");
        AgentChatParam agentChatParam = new AgentChatParam();
        if (arr.contains("NONE_OF_THE_ABOVE")) {
            agentChatParam.setQuery("我认为你提供的几张表不能满足我的要求, 请重新思考并完成`任务目标`");
        } else {
            agentChatParam.setQuery(arr.toJSONString());
        }
        agentChatParam.setAgent((String) parameterMap.get("agentId"));
        agentChatParam.setResponse_topic(topic);
        agentChatParam.setUser_id(session.getUserid());
        return response.setBody(JSONObject.parseObject(mossFeignClient.agentQuery(agentChatParam)));
    }

    @Override
    @SuppressWarnings("unchecked")
    public Response resultEvaluate(Map<String, Object> parameterMap, Session session) {
        double rate = Utils.parseDouble(parameterMap.get("rate"));
        if (rate > 4) {
            List<String> tables = (List<String>) parameterMap.get("tables");
            tables.sort(String::compareTo);
            parameterMap.put("templateGroup", StringUtils.remove(JSONArray.toJSONString(tables), " "));
            mossDao.saveGoodEvaluateResult(parameterMap);
        }
        parameterMap.put("chatId", Utils.randomStr(8));
        mossDao.saveEvaluateResult(parameterMap);
        return response;
    }

    @Override
    public Response queryTableTips(Map<String, Object> parameterMap) {
        List<Map<String, Object>> tips = mossDao.queryTableTips(parameterMap);
        Map<String, Object> reusltMap = new HashMap<>();
        for (Map<String, Object> tip : tips) {
            reusltMap.put((String) tip.get("OBJ_NAME"), tip.get("TIPS"));
            reusltMap.put("SCPM." + tip.get("OBJ_NAME"), tip.get("TIPS"));
        }
        return response.setBody(reusltMap);
    }

    @Override
    public Response queryChatLogs(Map<String, Object> parameterMap) {
        return response.setBody(mossDao.queryChatLogs(parameterMap));
    }

    @Override
    public Response queryChatById(Map<String, Object> parameterMap) {
        return response.setBody(mossDao.queryChatById(parameterMap));
    }

    @Override
    public Response queryAllChatLogs(Map<String, Object> parameterMap) {
        List<Map<String, Object>> dataList = mossDao.queryAllChatLogs(parameterMap);
        for (Map<String, Object> data : dataList) {
            JSONArray logs = JSONObject.parseArray((String) data.getOrDefault("LOGS", "[]"));
            List<String> temp = new ArrayList<>();
            int logSize = 0;
            for (int i = logs.size() - 1; i >= 0; i--) {
                JSONObject log = logs.getJSONObject(i);
                String query = log.getString("query");
                if ("assistant".equals(log.getString("role")) && StringUtils.isNotBlank(query) && "text".equals(log.getString("type"))) {
                    temp.add(query);
                    logSize += query.length();
                    if (logSize > 128) {
                        break;
                    }
                }
            }

            String result;
            if (temp.isEmpty()) {
                result = (String) data.get("SUBJECT");
            } else {
                Collections.reverse(temp);
                result = StringUtils.join(temp, "");
            }
            String keyword = (String) parameterMap.get("keyword");
            data.put("SUBJECT", StringUtils.replace((String) data.get("SUBJECT"), keyword, "<b style=\"color:var(--scp-text-color-highlight)\">" + keyword + "</b>"));
            data.put("LOGS", StringUtils.replace(result, keyword, "<b style=\"color:var(--scp-text-color-highlight)\">" + keyword + "</b>"));
        }
        return response.setBody(dataList);
    }

    @Override
    public Response querySuggestQuestions(Map<String, Object> parameterMap) {
        List<String> result = mossDao.querySuggestQuestions(parameterMap);
        Collections.shuffle(result);
        if (result.size() > 5) {
            // 随机打乱
            Collections.shuffle(result);
            return response.setBody(result.subList(0, 5));
        } else {
            return response.setBody(result);
        }
    }

    @Override
    public Response queryAllAgents(Map<String, Object> parameterMap) {
        List<Map<String, String>> dataList = mossDao.queryAllAgents(parameterMap);
        Map<String, List<Map<String, String>>> result = new HashMap<>();
        for (Map<String, String> data : dataList) {
            result.computeIfAbsent(data.get("GROUPS"), k -> new ArrayList<>()).add(data);
        }
        return response.setBody(result);
    }

    @Override
    public Response queryAgentById(Map<String, Object> parameterMap) {
        return response.setBody(mossDao.queryAgentById(parameterMap));
    }

    @Override
    public Response addToArchive(Map<String, Object> parameterMap) {
        parameterMap.put("id", Utils.randomStr(8));
        mossDao.addToArchive(parameterMap);
        return response;
    }

    @Override
    public Response queryMyArchive(Map<String, Object> parameterMap) {
        return response.setBody(mossDao.queryMyArchive(parameterMap));
    }

    @Override
    public Response deleteMyArchive(Map<String, Object> parameterMap) {
        mossDao.deleteMyArchive(parameterMap);
        return response;
    }
}
